amcl:
  ros__parameters:
    use_sim_time: True

    # 基本参数
    alpha1: 0.2
    alpha2: 0.2
    alpha3: 0.2
    alpha4: 0.2
    alpha5: 0.2

    # 坐标系设置
    base_frame_id: "base_link"
    global_frame_id: "map"
    odom_frame_id: "odom"

    # 激光扫描参数 - 优化性能
    laser_likelihood_max_dist: 2.0
    laser_max_range: 2.0  # 限制激光扫描范围
    laser_min_range: 0.12  # 设置合理的最小扫描范围
    laser_model_type: "likelihood_field"
    max_beams: 20  # 大幅减少激光束数量以提高性能

    # 粒子滤波器参数 - 优化性能
    max_particles: 500   # 大幅减少粒子数量
    min_particles: 100   # 减少最小粒子数量
    pf_err: 0.05
    pf_z: 0.99

    # 运动模型参数
    robot_model_type: "nav2_amcl::DifferentialMotionModel"

    # 更新阈值 - 使AMCL更容易跟踪机器人运动
    update_min_a: 0.05  # 角度更新阈值
    update_min_d: 0.05  # 距离更新阈值

    # 重采样参数
    resample_interval: 1

    # 激光模型参数
    sigma_hit: 0.2
    z_hit: 0.5
    z_max: 0.05
    z_rand: 0.5
    z_short: 0.05
    lambda_short: 0.1

    # 光束跳跃参数
    beam_skip_distance: 0.5
    beam_skip_threshold: 0.3
    do_beamskip: false

    # TF和时间相关参数 - 关键优化
    tf_broadcast: true
    transform_tolerance: 30.0  # 极大增加变换容忍度以处理仿真延迟
    save_pose_rate: 0.1  # 降低保存频率

    # 话题设置
    scan_topic: scan
    map_topic: map

    # 消息过滤器参数 - 关键优化
    tf_message_filter_queue_size: 1000  # 极大增加队列大小

    # 禁用一些不必要的功能以减少计算负担
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0

    # 初始位姿设置
    set_initial_pose: true
    initial_pose:
      x: 1.17
      y: -1.5
      z: 0.0
      yaw: 0.0

map_server:
  ros__parameters:
    yaml_filename: "/opt/overlay_ws/src/navigation2/nav2_system_tests/maps/blank_map_1000x1000.yaml"
    use_sim_time: True
