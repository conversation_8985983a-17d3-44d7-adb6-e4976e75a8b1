amcl:
  ros__parameters:
    # 增加消息过滤器队列大小和变换容忍度
    transform_tolerance: 5.0  # 大幅增加变换容忍度以处理仿真延迟
    laser_max_beams: 30  # 减少激光束数量
    laser_model_type: "likelihood_field"  # 使用更高效的激光模型
    tf_message_filter_queue_size: 200  # 大幅增加TF消息过滤器队列大小
    # 增加激光扫描相关的容忍度设置
    laser_max_range: 3.5  # 限制激光扫描范围以减少计算负担
    laser_min_range: 0.12  # 设置最小扫描范围
    # 降低粒子数量以减少计算负担
    max_particles: 1000  # 从默认2000降低到1000
    min_particles: 200   # 从默认500降低到200

local_costmap:
  local_costmap:
    ros__parameters:
      transform_tolerance: 2.0  # 增加变换容忍度
      voxel_layer:
        scan:
          observation_keep_time: 0.5  # 减少观察保持时间
          expected_update_rate: 0.5  # 降低期望更新频率
          tf_message_filter_queue_size: 100  # 增加TF消息过滤器队列大小

global_costmap:
  global_costmap:
    ros__parameters:
      transform_tolerance: 2.0  # 增加变换容忍度
      obstacle_layer:
        scan:
          observation_keep_time: 0.5  # 减少观察保持时间
          expected_update_rate: 0.5  # 降低期望更新频率
          tf_message_filter_queue_size: 100  # 增加TF消息过滤器队列大小
